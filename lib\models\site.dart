import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'user.dart';

part 'site.g.dart';

// Helper function to convert string to double
double _doubleFromString(dynamic value) {
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) return double.parse(value);
  throw ArgumentError('Cannot convert $value to double');
}

@JsonSerializable()
class Site extends Equatable {
  final int id;
  final String name;
  @JsonKey(fromJson: _doubleFromString)
  final double latitude;
  @Json<PERSON>ey(fromJson: _doubleFromString)
  final double longitude;
  final List<User>? users;
  @JsonKey(name: 'pointages_count')
  final int? pointagesCount;
  @<PERSON>son<PERSON>ey(name: 'created_at')
  final String? createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final String? updatedAt;

  const Site({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    this.users,
    this.pointagesCount,
    this.createdAt,
    this.updatedAt,
  });

  factory Site.fromJson(Map<String, dynamic> json) => _$SiteFromJson(json);

  Map<String, dynamic> toJson() => _$SiteToJson(this);

  Site copyWith({
    int? id,
    String? name,
    double? latitude,
    double? longitude,
    List<User>? users,
    int? pointagesCount,
    String? createdAt,
    String? updatedAt,
  }) {
    return Site(
      id: id ?? this.id,
      name: name ?? this.name,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      users: users ?? this.users,
      pointagesCount: pointagesCount ?? this.pointagesCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get displayName => name;
  String get coordinates => '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';

  @override
  List<Object?> get props => [id, name, latitude, longitude, users, pointagesCount, createdAt, updatedAt];

  @override
  String toString() {
    return 'Site{id: $id, name: $name, latitude: $latitude, longitude: $longitude}';
  }
}

@JsonSerializable()
class SiteCreateRequest extends Equatable {
  final String name;
  final double latitude;
  final double longitude;

  const SiteCreateRequest({
    required this.name,
    required this.latitude,
    required this.longitude,
  });

  factory SiteCreateRequest.fromJson(Map<String, dynamic> json) =>
      _$SiteCreateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SiteCreateRequestToJson(this);

  @override
  List<Object?> get props => [name, latitude, longitude];
}

@JsonSerializable()
class SiteUpdateRequest extends Equatable {
  final String? name;
  final double? latitude;
  final double? longitude;

  const SiteUpdateRequest({
    this.name,
    this.latitude,
    this.longitude,
  });

  factory SiteUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$SiteUpdateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SiteUpdateRequestToJson(this);

  @override
  List<Object?> get props => [name, latitude, longitude];
}

@JsonSerializable()
class SiteAssignmentRequest extends Equatable {
  @JsonKey(name: 'user_id')
  final int userId;
  @JsonKey(name: 'site_id')
  final int siteId;

  const SiteAssignmentRequest({
    required this.userId,
    required this.siteId,
  });

  factory SiteAssignmentRequest.fromJson(Map<String, dynamic> json) =>
      _$SiteAssignmentRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SiteAssignmentRequestToJson(this);

  @override
  List<Object?> get props => [userId, siteId];
}

@JsonSerializable()
class LocationCheckRequest extends Equatable {
  @JsonKey(name: 'site_id')
  final int siteId;
  final double latitude;
  final double longitude;

  const LocationCheckRequest({
    required this.siteId,
    required this.latitude,
    required this.longitude,
  });

  factory LocationCheckRequest.fromJson(Map<String, dynamic> json) =>
      _$LocationCheckRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LocationCheckRequestToJson(this);

  @override
  List<Object?> get props => [siteId, latitude, longitude];
}

@JsonSerializable()
class LocationCheckResponse extends Equatable {
  @JsonKey(name: 'in_range')
  final bool inRange;
  final double distance;
  @JsonKey(name: 'max_distance')
  final double maxDistance;
  final Site site;

  const LocationCheckResponse({
    required this.inRange,
    required this.distance,
    required this.maxDistance,
    required this.site,
  });

  factory LocationCheckResponse.fromJson(Map<String, dynamic> json) =>
      _$LocationCheckResponseFromJson(json);

  Map<String, dynamic> toJson() => _$LocationCheckResponseToJson(this);

  String get distanceText => '${distance.toStringAsFixed(1)} متر';
  String get maxDistanceText => '${maxDistance.toStringAsFixed(0)} متر';
  String get statusText => inRange ? 'داخل النطاق' : 'خارج النطاق';

  @override
  List<Object?> get props => [inRange, distance, maxDistance, site];
}

@JsonSerializable()
class SiteWithAssignments extends Equatable {
  final Site site;
  @JsonKey(name: 'assigned_users')
  final List<int> assignedUsers;
  @JsonKey(name: 'active_pointages')
  final int activePointages;

  const SiteWithAssignments({
    required this.site,
    required this.assignedUsers,
    required this.activePointages,
  });

  factory SiteWithAssignments.fromJson(Map<String, dynamic> json) =>
      _$SiteWithAssignmentsFromJson(json);

  Map<String, dynamic> toJson() => _$SiteWithAssignmentsToJson(this);

  int get id => site.id;
  String get name => site.name;
  double get latitude => site.latitude;
  double get longitude => site.longitude;
  String get coordinates => site.coordinates;
  int get assignedUsersCount => assignedUsers.length;
  bool get hasActivePointages => activePointages > 0;

  @override
  List<Object?> get props => [site, assignedUsers, activePointages];
}
