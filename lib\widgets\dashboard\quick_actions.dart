import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../providers/attendance_provider.dart';
import '../common/custom_button.dart';

class QuickActions extends StatelessWidget {
  final AttendanceProvider attendanceProvider;

  const QuickActions({
    super.key,
    required this.attendanceProvider,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات سريعة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: attendanceProvider.getNextActionText(),
                    onPressed: () {
                      // TODO: Implement check-in/check-out functionality
                      _showComingSoonDialog(context);
                    },
                    icon: attendanceProvider.hasActivePointage 
                        ? Icons.logout 
                        : Icons.login,
                    type: attendanceProvider.hasActivePointage 
                        ? ButtonType.warning 
                        : ButtonType.success,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: CustomButton(
                    text: 'سجل الحضور',
                    onPressed: () {
                      // TODO: Navigate to attendance history
                      _showComingSoonDialog(context);
                    },
                    icon: Icons.history,
                    type: ButtonType.outline,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'تحديد الموقع',
                    onPressed: () {
                      // TODO: Implement location check
                      _showComingSoonDialog(context);
                    },
                    icon: Icons.my_location,
                    type: ButtonType.secondary,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: CustomButton(
                    text: 'الإعدادات',
                    onPressed: () {
                      // TODO: Navigate to settings
                      _showComingSoonDialog(context);
                    },
                    icon: Icons.settings,
                    type: ButtonType.text,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showComingSoonDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('قريباً'),
        content: const Text('هذه الميزة ستكون متاحة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }
}
