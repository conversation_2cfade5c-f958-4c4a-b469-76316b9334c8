import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../models/models.dart';
import '../../widgets/common/custom_error_widget.dart';
import '../../routes/app_routes.dart';

class PointagesManagementScreen extends StatefulWidget {
  const PointagesManagementScreen({super.key});

  @override
  State<PointagesManagementScreen> createState() => _PointagesManagementScreenState();
}

class _PointagesManagementScreenState extends State<PointagesManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  String? _selectedUserId;
  String? _selectedSiteId;
  DateTime? _dateFrom;
  DateTime? _dateTo;
  bool _showActiveOnly = false;

  @override
  void initState() {
    super.initState();
    _loadPointages();
  }

  void _loadPointages() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AttendanceProvider>().loadPointages(
        userId: _selectedUserId,
        siteId: _selectedSiteId,
        dateFrom: _dateFrom,
        dateTo: _dateTo,
        activeOnly: _showActiveOnly,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة سجلات الحضور'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.textWhite,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'تصفية',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportPointages,
            tooltip: 'تصدير Excel',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPointages,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildStatsHeader(),
          Expanded(child: _buildPointagesList()),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.surfaceWhite,
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow.withValues(alpha: 0.1),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في سجلات الحضور...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
              filled: true,
              fillColor: AppColors.inputBackground,
            ),
            onChanged: (value) {
              // Implement search functionality
            },
          ),
          if (_hasActiveFilters()) ...[
            const SizedBox(height: 12),
            _buildActiveFiltersChips(),
          ],
        ],
      ),
    );
  }

  Widget _buildStatsHeader() {
    return Consumer<AttendanceProvider>(
      builder: (context, provider, child) {
        final stats = provider.getPointagesStats();
        
        return Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي السجلات',
                  '${stats['total'] ?? 0}',
                  Icons.list_alt,
                  AppColors.primaryBlue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'نشط حالياً',
                  '${stats['active'] ?? 0}',
                  Icons.access_time,
                  AppColors.warning,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'مكتمل اليوم',
                  '${stats['completed_today'] ?? 0}',
                  Icons.check_circle,
                  AppColors.success,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPointagesList() {
    return Consumer<AttendanceProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.state == AttendanceState.error) {
          return CustomErrorWidget(
            message: provider.errorMessage ?? 'حدث خطأ في تحميل البيانات',
            onRetry: _loadPointages,
          );
        }

        final pointages = provider.pointages;

        if (pointages.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.builder(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          itemCount: pointages.length,
          itemBuilder: (context, index) {
            final pointage = pointages[index];
            return _buildPointageCard(pointage);
          },
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.access_time_outlined,
            size: 100,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 20),
          Text(
            'لا توجد سجلات حضور',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على سجلات حضور بالمعايير المحددة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              _clearFilters();
              _loadPointages();
            },
            icon: const Icon(Icons.clear_all),
            label: const Text('مسح المرشحات'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: AppColors.textWhite,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPointageCard(Pointage pointage) {
    final isActive = pointage.finPointage == null;
    final duration = pointage.duree ?? _calculateCurrentDuration(pointage.debutPointage);

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: isActive ? AppColors.warning : AppColors.success,
                  child: Icon(
                    isActive ? Icons.access_time : Icons.check,
                    color: AppColors.textWhite,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        pointage.user?.name ?? 'غير محدد',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        pointage.site?.name ?? 'غير محدد',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isActive 
                        ? AppColors.warning.withValues(alpha: 0.1)
                        : AppColors.success.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isActive 
                          ? AppColors.warning.withValues(alpha: 0.3)
                          : AppColors.success.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    isActive ? 'نشط' : 'مكتمل',
                    style: TextStyle(
                      color: isActive ? AppColors.warning : AppColors.success,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildPointageDetails(pointage, duration),
          ],
        ),
      ),
    );
  }

  Widget _buildPointageDetails(Pointage pointage, String duration) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildDetailItem(
                'بداية الحضور',
                _formatDateTime(pointage.debutPointage),
                Icons.login,
                AppColors.success,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDetailItem(
                'نهاية الحضور',
                pointage.finPointage != null 
                    ? _formatDateTime(pointage.finPointage!)
                    : 'لم ينته بعد',
                Icons.logout,
                pointage.finPointage != null ? AppColors.error : AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _buildDetailItem(
          'المدة الإجمالية',
          duration,
          Icons.timer,
          AppColors.primaryBlue,
        ),
      ],
    );
  }

  Widget _buildDetailItem(String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActiveFiltersChips() {
    return Wrap(
      spacing: 8,
      children: [
        if (_selectedUserId != null)
          _buildFilterChip('موظف محدد', () => setState(() => _selectedUserId = null)),
        if (_selectedSiteId != null)
          _buildFilterChip('موقع محدد', () => setState(() => _selectedSiteId = null)),
        if (_dateFrom != null)
          _buildFilterChip('من تاريخ', () => setState(() => _dateFrom = null)),
        if (_dateTo != null)
          _buildFilterChip('إلى تاريخ', () => setState(() => _dateTo = null)),
        if (_showActiveOnly)
          _buildFilterChip('نشط فقط', () => setState(() => _showActiveOnly = false)),
      ],
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onRemove) {
    return Chip(
      label: Text(label),
      deleteIcon: const Icon(Icons.close, size: 16),
      onDeleted: onRemove,
      backgroundColor: AppColors.primaryBlueLight,
      labelStyle: const TextStyle(color: AppColors.primaryBlue),
    );
  }

  bool _hasActiveFilters() {
    return _selectedUserId != null ||
           _selectedSiteId != null ||
           _dateFrom != null ||
           _dateTo != null ||
           _showActiveOnly;
  }

  void _clearFilters() {
    setState(() {
      _selectedUserId = null;
      _selectedSiteId = null;
      _dateFrom = null;
      _dateTo = null;
      _showActiveOnly = false;
    });
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _calculateCurrentDuration(DateTime start) {
    final now = DateTime.now();
    final difference = now.difference(start);
    final hours = difference.inHours;
    final minutes = difference.inMinutes % 60;
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:00';
  }

  void _showFilterDialog() {
    // Implementation for filter dialog
  }

  void _exportPointages() {
    // Implementation for export functionality
  }
}
