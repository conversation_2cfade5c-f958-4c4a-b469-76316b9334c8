import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../models/models.dart';
import '../../widgets/common/custom_error_widget.dart';

class AttendanceHistoryScreen extends StatefulWidget {
  const AttendanceHistoryScreen({super.key});

  @override
  State<AttendanceHistoryScreen> createState() => _AttendanceHistoryScreenState();
}

class _AttendanceHistoryScreenState extends State<AttendanceHistoryScreen> {
  DateTime? _startDate;
  DateTime? _endDate;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _loadAttendanceHistory();
  }

  void _loadAttendanceHistory() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AttendanceProvider>().loadAttendanceData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('سجل الحضور'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.textWhite,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAttendanceHistory,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterChips(),
          _buildStatsCards(),
          Expanded(
            child: Consumer<AttendanceProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (provider.state == AttendanceState.error) {
                  return CustomErrorWidget(
                    message: provider.errorMessage ?? 'حدث خطأ في تحميل البيانات',
                    onRetry: _loadAttendanceHistory,
                  );
                }

                final filteredPointages = _getFilteredPointages(provider.pointages);

                if (filteredPointages.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.history, size: 100, color: Colors.grey),
                        SizedBox(height: 20),
                        Text(
                          'لا يوجد سجل حضور',
                          style: TextStyle(fontSize: 18, color: Colors.grey),
                        ),
                      ],
                    ),
                  );
                }

                return _buildAttendanceList(filteredPointages);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildFilterChip('all', 'الكل'),
            const SizedBox(width: 8),
            _buildFilterChip('today', 'اليوم'),
            const SizedBox(width: 8),
            _buildFilterChip('week', 'هذا الأسبوع'),
            const SizedBox(width: 8),
            _buildFilterChip('month', 'هذا الشهر'),
            const SizedBox(width: 8),
            _buildFilterChip('custom', 'فترة مخصصة'),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = value;
          if (value == 'custom') {
            _showDateRangePicker();
          } else {
            _startDate = null;
            _endDate = null;
          }
        });
      },
      selectedColor: AppColors.primaryBlue.withValues(alpha: 0.2),
      checkmarkColor: AppColors.primaryBlue,
    );
  }

  Widget _buildStatsCards() {
    return Consumer<AttendanceProvider>(
      builder: (context, provider, child) {
        final filteredPointages = _getFilteredPointages(provider.pointages);
        final totalHours = _calculateTotalHours(filteredPointages);
        final totalDays = _calculateTotalDays(filteredPointages);
        final averageHours = totalDays > 0 ? totalHours / totalDays : 0.0;

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
          child: Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الساعات',
                  '${totalHours.toStringAsFixed(1)}س',
                  Icons.access_time,
                  AppColors.primaryBlue,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: _buildStatCard(
                  'أيام العمل',
                  '$totalDays',
                  Icons.calendar_today,
                  AppColors.success,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: _buildStatCard(
                  'متوسط الساعات',
                  '${averageHours.toStringAsFixed(1)}س',
                  Icons.trending_up,
                  AppColors.warning,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAttendanceList(List<Pointage> pointages) {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: pointages.length,
      itemBuilder: (context, index) {
        final pointage = pointages[index];
        return _buildAttendanceCard(pointage);
      },
    );
  }

  Widget _buildAttendanceCard(Pointage pointage) {
    final startTime = pointage.debutPointage != null 
        ? DateTime.parse(pointage.debutPointage!) 
        : null;
    final endTime = pointage.finPointage != null 
        ? DateTime.parse(pointage.finPointage!) 
        : null;
    
    final duration = startTime != null && endTime != null
        ? endTime.difference(startTime)
        : null;

    final isActive = pointage.finPointage == null;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: (isActive ? AppColors.success : AppColors.info)
                        .withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    isActive ? Icons.work : Icons.work_off,
                    color: isActive ? AppColors.success : AppColors.info,
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        startTime != null 
                            ? DateFormat('EEEE، dd MMMM yyyy', 'ar').format(startTime)
                            : 'تاريخ غير محدد',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        isActive ? 'جلسة عمل نشطة' : 'جلسة عمل مكتملة',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: isActive ? AppColors.success : AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: (isActive ? AppColors.success : AppColors.info)
                        .withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: (isActive ? AppColors.success : AppColors.info)
                          .withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    isActive ? 'نشط' : 'مكتمل',
                    style: TextStyle(
                      color: isActive ? AppColors.success : AppColors.info,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: _buildTimeInfo(
                    'وقت الدخول',
                    startTime != null 
                        ? DateFormat('HH:mm').format(startTime)
                        : '--:--',
                    Icons.login,
                    AppColors.success,
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: _buildTimeInfo(
                    'وقت الخروج',
                    endTime != null 
                        ? DateFormat('HH:mm').format(endTime)
                        : isActive ? 'لم ينته' : '--:--',
                    Icons.logout,
                    isActive ? AppColors.warning : AppColors.error,
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: _buildTimeInfo(
                    'إجمالي الوقت',
                    duration != null 
                        ? '${duration.inHours}س ${duration.inMinutes % 60}د'
                        : isActive ? 'جاري...' : '--',
                    Icons.timer,
                    AppColors.primaryBlue,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeInfo(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  List<Pointage> _getFilteredPointages(List<Pointage> pointages) {
    final now = DateTime.now();
    
    return pointages.where((pointage) {
      if (pointage.debutPointage == null) return false;
      
      final pointageDate = DateTime.parse(pointage.debutPointage!);
      
      switch (_selectedFilter) {
        case 'today':
          return pointageDate.year == now.year &&
                 pointageDate.month == now.month &&
                 pointageDate.day == now.day;
        case 'week':
          final weekStart = now.subtract(Duration(days: now.weekday - 1));
          return pointageDate.isAfter(weekStart.subtract(const Duration(days: 1)));
        case 'month':
          return pointageDate.year == now.year &&
                 pointageDate.month == now.month;
        case 'custom':
          if (_startDate != null && _endDate != null) {
            return pointageDate.isAfter(_startDate!.subtract(const Duration(days: 1))) &&
                   pointageDate.isBefore(_endDate!.add(const Duration(days: 1)));
          }
          return true;
        default:
          return true;
      }
    }).toList();
  }

  double _calculateTotalHours(List<Pointage> pointages) {
    double total = 0;
    for (final pointage in pointages) {
      if (pointage.debutPointage != null) {
        final start = DateTime.parse(pointage.debutPointage!);
        final end = pointage.finPointage != null 
            ? DateTime.parse(pointage.finPointage!)
            : DateTime.now();
        total += end.difference(start).inMinutes / 60.0;
      }
    }
    return total;
  }

  int _calculateTotalDays(List<Pointage> pointages) {
    final uniqueDays = <String>{};
    for (final pointage in pointages) {
      if (pointage.debutPointage != null) {
        final date = DateTime.parse(pointage.debutPointage!);
        uniqueDays.add('${date.year}-${date.month}-${date.day}');
      }
    }
    return uniqueDays.length;
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية السجل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('الكل'),
              leading: Radio<String>(
                value: 'all',
                groupValue: _selectedFilter,
                onChanged: (value) {
                  setState(() {
                    _selectedFilter = value!;
                  });
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              title: const Text('اليوم'),
              leading: Radio<String>(
                value: 'today',
                groupValue: _selectedFilter,
                onChanged: (value) {
                  setState(() {
                    _selectedFilter = value!;
                  });
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              title: const Text('هذا الأسبوع'),
              leading: Radio<String>(
                value: 'week',
                groupValue: _selectedFilter,
                onChanged: (value) {
                  setState(() {
                    _selectedFilter = value!;
                  });
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              title: const Text('هذا الشهر'),
              leading: Radio<String>(
                value: 'month',
                groupValue: _selectedFilter,
                onChanged: (value) {
                  setState(() {
                    _selectedFilter = value!;
                  });
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _startDate != null && _endDate != null
          ? DateTimeRange(start: _startDate!, end: _endDate!)
          : null,
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
    }
  }
}
