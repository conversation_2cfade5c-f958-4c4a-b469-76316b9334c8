import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../providers/providers.dart';
import '../../models/models.dart';
import '../../services/api_service.dart';

class SitesDebugScreen extends StatefulWidget {
  const SitesDebugScreen({super.key});

  @override
  State<SitesDebugScreen> createState() => _SitesDebugScreenState();
}

class _SitesDebugScreenState extends State<SitesDebugScreen> {
  @override
  void initState() {
    super.initState();
    _loadSites();
  }

  void _loadSites() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SitesProvider>().refreshSites();
    });
  }

  void _testApiDirectly() async {
    debugPrint('=== TEST API DIRECT ===');
    try {
      final apiService = ApiService();
      debugPrint('Testing direct API call...');

      final response = await apiService.getSites(page: 1, perPage: 10);
      debugPrint('API Response received:');
      debugPrint('- Data count: ${response.data.length}');
      debugPrint('- Total: ${response.total}');
      debugPrint('- Current page: ${response.currentPage}');
      debugPrint('- Last page: ${response.lastPage}');

      if (response.data.isNotEmpty) {
        debugPrint('First site: ${response.data.first.name} (ID: ${response.data.first.id})');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('API Test: ${response.data.length} sites trouvés'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      debugPrint('API Test Error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur API: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
    debugPrint('=== FIN TEST API ===');
  }

  void _loadTestData() {
    debugPrint('=== CHARGEMENT DONNÉES TEST ===');

    // Créer des sites de test
    final testSites = [
      Site(
        id: 1,
        name: 'Site Test Alger',
        latitude: 36.7538,
        longitude: 3.0588,
        createdAt: DateTime.now().toIso8601String(),
        updatedAt: DateTime.now().toIso8601String(),
      ),
      Site(
        id: 2,
        name: 'Site Test Oran',
        latitude: 35.6911,
        longitude: -0.6417,
        createdAt: DateTime.now().toIso8601String(),
        updatedAt: DateTime.now().toIso8601String(),
      ),
      Site(
        id: 3,
        name: 'Site Test Constantine',
        latitude: 36.3650,
        longitude: 6.6147,
        createdAt: DateTime.now().toIso8601String(),
        updatedAt: DateTime.now().toIso8601String(),
      ),
    ];

    // Injecter les données de test dans le provider
    final provider = context.read<SitesProvider>();
    provider.setTestSites(testSites);

    debugPrint('Données test chargées: ${testSites.length} sites');

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${testSites.length} sites de test chargés'),
          backgroundColor: AppColors.success,
        ),
      );
    }

    debugPrint('=== FIN CHARGEMENT DONNÉES TEST ===');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sites Debug'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.textWhite,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSites,
          ),
          IconButton(
            icon: const Icon(Icons.science),
            onPressed: _testApiDirectly,
            tooltip: 'Test API Direct',
          ),
          IconButton(
            icon: const Icon(Icons.data_object),
            onPressed: _loadTestData,
            tooltip: 'Charger données test',
          ),
        ],
      ),
      body: Consumer<SitesProvider>(
        builder: (context, provider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStateInfo(provider),
                const SizedBox(height: 20),
                _buildSitesInfo(provider),
                const SizedBox(height: 20),
                _buildSitesList(provider.sites),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStateInfo(SitesProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'État du Provider',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text('État: ${provider.state}'),
            Text('Chargement: ${provider.isLoading}'),
            Text('Erreur: ${provider.errorMessage ?? "Aucune"}'),
            Text('Page actuelle: ${provider.currentPage}'),
            Text('Plus de données: ${provider.hasMoreData}'),
          ],
        ),
      ),
    );
  }

  Widget _buildSitesInfo(SitesProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations des Sites',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text('Nombre total: ${provider.sites.length}'),
            Text('Sites avec coordonnées: ${provider.sites.where((s) => s.latitude != 0 && s.longitude != 0).length}'),
            Text('Sites sans coordonnées: ${provider.sites.where((s) => s.latitude == 0 && s.longitude == 0).length}'),
          ],
        ),
      ),
    );
  }

  Widget _buildSitesList(List<Site> sites) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Liste des Sites (${sites.length})',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            if (sites.isEmpty)
              const Text('Aucun site trouvé')
            else
              ...sites.map((site) => _buildSiteItem(site)),
          ],
        ),
      ),
    );
  }

  Widget _buildSiteItem(Site site) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.borderLight),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ID: ${site.id} - ${site.name}',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Text('Latitude: ${site.latitude}'),
          Text('Longitude: ${site.longitude}'),
          Text('Coordonnées: ${site.coordinates}'),
          if (site.createdAt != null)
            Text('Créé le: ${site.createdAt}'),
          if (site.updatedAt != null)
            Text('Modifié le: ${site.updatedAt}'),
        ],
      ),
    );
  }
}
