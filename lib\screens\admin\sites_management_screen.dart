import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../models/models.dart';
import '../../widgets/common/custom_error_widget.dart';
import '../../routes/app_routes.dart';
import '../debug/sites_debug_screen.dart';

class SitesManagementScreen extends StatefulWidget {
  const SitesManagementScreen({super.key});

  @override
  State<SitesManagementScreen> createState() => _SitesManagementScreenState();
}

class _SitesManagementScreenState extends State<SitesManagementScreen> {
  bool _isMapView = false;
  GoogleMapController? _mapController;
  final Set<Marker> _markers = {};

  // Coordonnées par défaut pour l'Algérie (Alger)
  static const LatLng _algerCenter = LatLng(36.7538, 3.0588);

  // Villes principales d'Algérie avec leurs coordonnées
  final Map<String, LatLng> _algerianCities = {
    'الجزائر العاصمة': LatLng(36.7538, 3.0588),
    'وهران': LatLng(35.6911, -0.6417),
    'قسنطينة': LatLng(36.3650, 6.6147),
    'عنابة': LatLng(36.9000, 7.7667),
    'باتنة': LatLng(35.5667, 6.1667),
    'سطيف': LatLng(36.1833, 5.4167),
    'سيدي بلعباس': LatLng(35.2167, -0.6333),
    'بسكرة': LatLng(34.8500, 5.7333),
    'تبسة': LatLng(35.4000, 8.1167),
    'ورقلة': LatLng(31.9500, 5.3333),
  };

  @override
  void initState() {
    super.initState();
    _loadSites();
  }

  void _loadSites() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SitesProvider>().refreshSites();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المواقع'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.textWhite,
        actions: [
          if (_isMapView) ...[
            PopupMenuButton<String>(
              icon: const Icon(Icons.location_city),
              tooltip: 'الانتقال إلى مدينة',
              onSelected: _goToCity,
              itemBuilder: (context) => _algerianCities.keys
                  .map((city) => PopupMenuItem(
                        value: city,
                        child: Text(city),
                      ))
                  .toList(),
            ),
          ],
          IconButton(
            icon: Icon(_isMapView ? Icons.list : Icons.map),
            onPressed: () {
              setState(() {
                _isMapView = !_isMapView;
              });
              if (_isMapView) {
                _updateMapMarkers();
              }
            },
            tooltip: _isMapView ? 'عرض القائمة' : 'عرض الخريطة',
          ),
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SitesDebugScreen(),
                ),
              );
            },
            tooltip: 'Debug',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => Navigator.pushNamed(context, AppRoutes.addSite),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSites,
          ),
        ],
      ),
      body: Consumer<SitesProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.state == SitesState.error) {
            return CustomErrorWidget(
              message: provider.errorMessage ?? 'حدث خطأ في تحميل البيانات',
              onRetry: _loadSites,
            );
          }

          if (provider.sites.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.location_off,
                    size: 100,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'لا توجد مواقع',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'ابدأ بإضافة موقع جديد',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pushNamed(context, AppRoutes.addSite);
                    },
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة موقع'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryBlue,
                      foregroundColor: AppColors.textWhite,
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextButton.icon(
                    onPressed: () {
                      debugPrint('Manual refresh requested');
                      _loadSites();
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة تحميل'),
                  ),
                ],
              ),
            );
          }

          return _isMapView ? _buildMapView(provider.sites) : _buildListView(provider.sites);
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Navigator.pushNamed(context, AppRoutes.addSite),
        backgroundColor: AppColors.primaryBlue,
        child: const Icon(Icons.add, color: AppColors.textWhite),
      ),
    );
  }

  Widget _buildListView(List<Site> sites) {
    debugPrint('SitesManagementScreen: Building list view with ${sites.length} sites');

    return Column(
      children: [
        _buildStatsHeader(sites),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: sites.length,
            itemBuilder: (context, index) {
              final site = sites[index];
              debugPrint('SitesManagementScreen: Building card for site: ${site.name} (${site.id})');
              return _buildSiteCard(site);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatsHeader(List<Site> sites) {
    final sitesWithCoordinates = sites.where((s) => s.latitude != 0 && s.longitude != 0).length;
    
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.surfaceWhite,
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow.withValues(alpha: 0.1),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              title: 'إجمالي المواقع',
              value: '${sites.length}',
              icon: Icons.location_on,
              color: AppColors.primaryBlue,
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: _buildStatItem(
              title: 'مواقع محددة',
              value: '$sitesWithCoordinates',
              icon: Icons.gps_fixed,
              color: AppColors.success,
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: _buildStatItem(
              title: 'مواقع نشطة',
              value: '${sites.length}', // TODO: Add active sites count
              icon: Icons.radio_button_checked,
              color: AppColors.warning,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSiteCard(Site site) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.primaryBlueLight,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.location_on,
                    color: AppColors.primaryBlue,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        site.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'ID: ${site.id}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleSiteAction(value, site),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'view',
                      child: Row(
                        children: [
                          Icon(Icons.visibility),
                          SizedBox(width: 8),
                          Text('عرض التفاصيل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'assign',
                      child: Row(
                        children: [
                          Icon(Icons.people, color: AppColors.primaryBlue),
                          SizedBox(width: 8),
                          Text('تعيين الموظفين'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: AppColors.error),
                          SizedBox(width: 8),
                          Text('حذف'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    icon: Icons.gps_fixed,
                    label: 'خط العرض',
                    value: site.latitude.toStringAsFixed(6),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: _buildInfoItem(
                    icon: Icons.gps_not_fixed,
                    label: 'خط الطول',
                    value: site.longitude.toStringAsFixed(6),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    icon: Icons.people,
                    label: 'الموظفون المعينون',
                    value: '${site.users?.length ?? 0}',
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: _buildInfoItem(
                    icon: Icons.access_time,
                    label: 'تسجيلات الحضور',
                    value: '${site.pointagesCount ?? 0}',
                  ),
                ),
              ],
            ),
            if (site.users != null && site.users!.isNotEmpty) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              _buildAssignedEmployeesSection(site.users!),
            ],
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.success.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
                  ),
                  child: const Text(
                    'نشط',
                    style: TextStyle(
                      color: AppColors.success,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: () => _handleSiteAction('assign', site),
                  icon: const Icon(Icons.people, size: 16),
                  label: const Text('تعيين'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.primaryBlue,
                  ),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _showOnMap(site),
                  icon: const Icon(Icons.map, size: 16),
                  label: const Text('خريطة'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: AppColors.textSecondary),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAssignedEmployeesSection(List<User> users) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.primaryBlueLight,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.primaryBlue.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.people,
                size: 16,
                color: AppColors.primaryBlue,
              ),
              const SizedBox(width: 8),
              Text(
                'الموظفون المعينون (${users.length})',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.primaryBlue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: users.take(3).map((user) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.surfaceWhite,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.primaryBlue.withValues(alpha: 0.2)),
              ),
              child: Text(
                user.name,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.primaryBlue,
                  fontWeight: FontWeight.w500,
                ),
              ),
            )).toList()
              ..addAll(users.length > 3 ? [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.surfaceWhite,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.primaryBlue.withValues(alpha: 0.2)),
                  ),
                  child: Text(
                    '+${users.length - 3}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ] : []),
          ),
        ],
      ),
    );
  }

  Widget _buildMapView(List<Site> sites) {
    return GoogleMap(
      onMapCreated: (GoogleMapController controller) {
        _mapController = controller;
        _updateMapMarkers();
      },
      initialCameraPosition: const CameraPosition(
        target: _algerCenter, // Coordonnées d'Alger
        zoom: 10,
      ),
      markers: _markers,
      onTap: (LatLng position) {
        // TODO: Add new site at tapped position
      },
    );
  }

  void _updateMapMarkers() {
    final sites = context.read<SitesProvider>().sites;
    _markers.clear();
    
    for (final site in sites) {
      if (site.latitude != 0 && site.longitude != 0) {
        _markers.add(
          Marker(
            markerId: MarkerId(site.id.toString()),
            position: LatLng(site.latitude, site.longitude),
            infoWindow: InfoWindow(
              title: site.name,
              snippet: 'ID: ${site.id}',
              onTap: () => _handleSiteAction('view', site),
            ),
          ),
        );
      }
    }
    
    if (mounted) {
      setState(() {});
    }
  }

  void _handleSiteAction(String action, Site site) {
    switch (action) {
      case 'view':
        Navigator.pushNamed(
          context,
          AppRoutes.siteDetails,
          arguments: {'siteId': site.id},
        );
        break;
      case 'assign':
        Navigator.pushNamed(
          context,
          AppRoutes.siteAssignment,
          arguments: {'site': site},
        ).then((result) {
          // Refresh sites if assignment was successful
          if (result == true) {
            _loadSites();
          }
        });
        break;
      case 'edit':
        Navigator.pushNamed(
          context,
          AppRoutes.editSite,
          arguments: {'site': site},
        );
        break;
      case 'delete':
        _showDeleteDialog(site);
        break;
    }
  }

  void _showOnMap(Site site) {
    setState(() {
      _isMapView = true;
    });
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _mapController?.animateCamera(
        CameraUpdate.newLatLngZoom(
          LatLng(site.latitude, site.longitude),
          15,
        ),
      );
    });
  }

  void _showDeleteDialog(Site site) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الموقع "${site.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteSite(site);
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  // Méthode pour naviguer vers une ville algérienne
  void _goToCity(String cityName) {
    final location = _algerianCities[cityName];
    if (location != null && _mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(location, 12),
      );
    }
  }

  void _deleteSite(Site site) async {
    try {
      await context.read<SitesProvider>().deleteSite(site.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف الموقع بنجاح')),
        );
        _updateMapMarkers();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في حذف الموقع: $e')),
        );
      }
    }
  }
}
