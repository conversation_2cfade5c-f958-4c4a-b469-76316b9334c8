import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';

class MonitoringScreen extends StatefulWidget {
  const MonitoringScreen({super.key});

  @override
  State<MonitoringScreen> createState() => _MonitoringScreenState();
}

class _MonitoringScreenState extends State<MonitoringScreen> {
  bool _isRealTimeEnabled = true;

  @override
  void initState() {
    super.initState();
    _loadData();
    if (_isRealTimeEnabled) {
      _startRealTimeUpdates();
    }
  }

  void _loadData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AttendanceProvider>().loadAttendanceData();
      context.read<SitesProvider>().loadSitesData();
    });
  }

  void _startRealTimeUpdates() {
    // TODO: Implement real-time updates using WebSocket or periodic refresh
    Future.delayed(const Duration(seconds: 30), () {
      if (mounted && _isRealTimeEnabled) {
        _loadData();
        _startRealTimeUpdates();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المراقبة المباشرة'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.textWhite,
        actions: [
          IconButton(
            icon: Icon(_isRealTimeEnabled ? Icons.pause : Icons.play_arrow),
            onPressed: () {
              setState(() {
                _isRealTimeEnabled = !_isRealTimeEnabled;
              });
              if (_isRealTimeEnabled) {
                _startRealTimeUpdates();
              }
            },
            tooltip: _isRealTimeEnabled ? 'إيقاف التحديث التلقائي' : 'تشغيل التحديث التلقائي',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async => _loadData(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildRealTimeStatus(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildLiveStats(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildActiveEmployees(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildSiteStatus(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildRecentActivity(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRealTimeStatus() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: _isRealTimeEnabled 
              ? [AppColors.success, AppColors.success.withValues(alpha: 0.8)]
              : [AppColors.warning, AppColors.warning.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [
          BoxShadow(
            color: (_isRealTimeEnabled ? AppColors.success : AppColors.warning)
                .withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.textWhite.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _isRealTimeEnabled ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: AppColors.textWhite,
              size: 24,
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _isRealTimeEnabled ? 'المراقبة المباشرة نشطة' : 'المراقبة المباشرة متوقفة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.textWhite,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _isRealTimeEnabled 
                      ? 'يتم تحديث البيانات كل 30 ثانية'
                      : 'اضغط على زر التشغيل لبدء المراقبة',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textWhite.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
          Text(
            DateFormat('HH:mm:ss').format(DateTime.now()),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLiveStats() {
    return Consumer2<AttendanceProvider, SitesProvider>(
      builder: (context, attendanceProvider, sitesProvider, child) {
        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'الموظفين النشطين',
                '${attendanceProvider.activeAttendanceCount}',
                Icons.people,
                AppColors.success,
                isLive: true,
              ),
            ),
            const SizedBox(width: AppConstants.smallPadding),
            Expanded(
              child: _buildStatCard(
                'الحضور اليوم',
                '${attendanceProvider.todayAttendanceCount}',
                Icons.access_time,
                AppColors.primaryBlue,
                isLive: true,
              ),
            ),
            const SizedBox(width: AppConstants.smallPadding),
            Expanded(
              child: _buildStatCard(
                'المواقع النشطة',
                '${sitesProvider.sites.length}',
                Icons.location_on,
                AppColors.warning,
                isLive: true,
              ),
            ),
            const SizedBox(width: AppConstants.smallPadding),
            Expanded(
              child: _buildStatCard(
                'التنبيهات',
                '3',
                Icons.warning,
                AppColors.error,
                isLive: true,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color, {bool isLive = false}) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(icon, color: color, size: 24),
              if (isLive)
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: _isRealTimeEnabled ? AppColors.success : AppColors.error,
                    shape: BoxShape.circle,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActiveEmployees() {
    return Consumer<AttendanceProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(AppConstants.defaultPadding),
              child: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        final activePointages = provider.pointages.where((p) => p.finPointage == null).toList();

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.people, color: AppColors.primaryBlue),
                    const SizedBox(width: 8),
                    Text(
                      'الموظفين النشطين (${activePointages.length})',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    if (_isRealTimeEnabled)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.success.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.circle, color: AppColors.success, size: 8),
                            SizedBox(width: 4),
                            Text(
                              'مباشر',
                              style: TextStyle(
                                color: AppColors.success,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                if (activePointages.isEmpty)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(AppConstants.largePadding),
                      child: Column(
                        children: [
                          Icon(Icons.people_outline, size: 60, color: Colors.grey),
                          SizedBox(height: 16),
                          Text(
                            'لا يوجد موظفين نشطين حالياً',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: activePointages.length,
                    separatorBuilder: (context, index) => const Divider(height: 1),
                    itemBuilder: (context, index) {
                      final pointage = activePointages[index];
                      final startTime = pointage.debutPointage != null 
                          ? DateTime.parse(pointage.debutPointage!)
                          : null;
                      final duration = startTime != null 
                          ? DateTime.now().difference(startTime)
                          : null;

                      return ListTile(
                        leading: CircleAvatar(
                          backgroundColor: AppColors.success.withValues(alpha: 0.1),
                          child: const Icon(Icons.work, color: AppColors.success),
                        ),
                        title: Text('موظف ${pointage.userId}'),
                        subtitle: Text(
                          startTime != null 
                              ? 'بدأ في: ${DateFormat('HH:mm').format(startTime)}'
                              : 'وقت البداية غير محدد',
                        ),
                        trailing: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: AppColors.success.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'نشط',
                                style: TextStyle(
                                  color: AppColors.success,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              duration != null 
                                  ? '${duration.inHours}:${(duration.inMinutes % 60).toString().padLeft(2, '0')}'
                                  : '--:--',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSiteStatus() {
    return Consumer<SitesProvider>(
      builder: (context, provider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.location_on, color: AppColors.primaryBlue),
                    const SizedBox(width: 8),
                    Text(
                      'حالة المواقع',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                if (provider.sites.isEmpty)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(AppConstants.largePadding),
                      child: Column(
                        children: [
                          Icon(Icons.location_off, size: 60, color: Colors.grey),
                          SizedBox(height: 16),
                          Text(
                            'لا توجد مواقع مسجلة',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: AppConstants.smallPadding,
                      mainAxisSpacing: AppConstants.smallPadding,
                      childAspectRatio: 1.5,
                    ),
                    itemCount: provider.sites.length,
                    itemBuilder: (context, index) {
                      final site = provider.sites[index];
                      return Container(
                        padding: const EdgeInsets.all(AppConstants.defaultPadding),
                        decoration: BoxDecoration(
                          color: AppColors.success.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                          border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.location_on, color: AppColors.success, size: 20),
                                const SizedBox(width: 4),
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: const BoxDecoration(
                                    color: AppColors.success,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              site.name,
                              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'نشط',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppColors.success,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              '${(index + 1) * 3} موظف',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentActivity() {
    return Consumer<AttendanceProvider>(
      builder: (context, provider, child) {
        final recentPointages = provider.pointages.take(10).toList();

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.history, color: AppColors.primaryBlue),
                    const SizedBox(width: 8),
                    Text(
                      'النشاط الأخير',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    if (_isRealTimeEnabled)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.info.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.update, color: AppColors.info, size: 12),
                            SizedBox(width: 4),
                            Text(
                              'تحديث تلقائي',
                              style: TextStyle(
                                color: AppColors.info,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                if (recentPointages.isEmpty)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(AppConstants.largePadding),
                      child: Column(
                        children: [
                          Icon(Icons.history, size: 60, color: Colors.grey),
                          SizedBox(height: 16),
                          Text(
                            'لا يوجد نشاط حديث',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: recentPointages.length,
                    separatorBuilder: (context, index) => const Divider(height: 1),
                    itemBuilder: (context, index) {
                      final pointage = recentPointages[index];
                      final isCheckIn = pointage.finPointage == null;
                      final time = pointage.debutPointage != null 
                          ? DateTime.parse(pointage.debutPointage!)
                          : DateTime.now();

                      return ListTile(
                        leading: CircleAvatar(
                          backgroundColor: (isCheckIn ? AppColors.success : AppColors.error)
                              .withValues(alpha: 0.1),
                          child: Icon(
                            isCheckIn ? Icons.login : Icons.logout,
                            color: isCheckIn ? AppColors.success : AppColors.error,
                          ),
                        ),
                        title: Text('موظف ${pointage.userId}'),
                        subtitle: Text(isCheckIn ? 'تسجيل دخول' : 'تسجيل خروج'),
                        trailing: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              DateFormat('HH:mm').format(time),
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              _getTimeAgo(time),
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getTimeAgo(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }
}
