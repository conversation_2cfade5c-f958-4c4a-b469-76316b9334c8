name: clockin
description: "ClockIn - Employee Attendance Management System"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Material Design
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296

  # HTTP & API
  http: ^1.2.2
  dio: ^5.7.0

  # State Management
  provider: ^6.1.2

  # Local Storage & Caching
  shared_preferences: ^2.3.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Location Services
  geolocator: ^10.1.0
  geocoding: ^3.0.0
  permission_handler: ^11.3.1

  # Forms & Validation
  flutter_form_builder: ^10.1.0
  form_builder_validators: ^11.0.0

  # UI Components & Loading
  flutter_spinkit: ^5.2.1
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0

  # Date & Time
  intl: ^0.20.2

  # JSON Serialization
  json_annotation: ^4.9.0

  # Utilities
  equatable: ^2.0.5
  uuid: ^4.5.1

  # Maps & Location
  google_maps_flutter: ^2.5.3
  location: ^5.0.3

  # Charts & Graphs (commented out for now)
  # fl_chart: ^0.70.1
  # syncfusion_flutter_charts: ^27.2.5

  # PDF & File Export
  pdf: ^3.11.1
  printing: ^5.13.4
  file_picker: ^8.1.6
  path_provider: ^2.1.5

  # Notifications
  flutter_local_notifications: ^18.0.1
  firebase_messaging: ^15.1.6

  # Image & Camera
  image_picker: ^1.1.2

  # QR Code
  qr_flutter: ^4.1.0

  # Calendar & Date
  table_calendar: ^3.1.2
  # syncfusion_flutter_calendar: ^27.2.5

  # Data Tables
  data_table_2: ^2.5.15
  # syncfusion_flutter_datagrid: ^27.2.5

  # Animation & UI
  lottie: ^3.2.0
  animations: ^2.0.11
  flutter_staggered_animations: ^1.1.1

  # Connectivity
  connectivity_plus: ^6.1.0
  internet_connection_checker: ^1.0.0+1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.13
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true

  # Generate localization files
  generate: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/

# Flutter Internationalization
flutter_intl:
  enabled: true
  class_name: S
  main_locale: ar
  arb_dir: lib/l10n
  output_dir: lib/generated
