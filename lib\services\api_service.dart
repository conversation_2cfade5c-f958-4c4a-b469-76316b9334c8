import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';
import '../models/models.dart';
import 'http_service.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  final HttpService _httpService = HttpService();

  // Authentication endpoints
  Future<LoginResponse> login(String email, String password) async {
    final request = LoginRequest(email: email, password: password);
    final response = await _httpService.post<LoginResponse>(
      AppConstants.loginEndpoint,
      data: request.toJson(),
      fromJson: (json) => LoginResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> logout() async {
    await _httpService.post(AppConstants.logoutEndpoint);
  }

  Future<User> getCurrentUser() async {
    final response = await _httpService.get<User>(
      AppConstants.userEndpoint,
      fromJson: (json) => User.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  // Sites endpoints
  Future<PaginatedResponse<Site>> getSites({
    int page = 1,
    int perPage = 20,
    String? search,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'per_page': perPage,
      if (search != null && search.isNotEmpty) 'search': search,
    };

    debugPrint('ApiService: Getting sites with params: $queryParams');

    final result = await _httpService.getPaginated<Site>(
      AppConstants.sitesEndpoint,
      queryParameters: queryParams,
      fromJson: (json) => Site.fromJson(json),
    );

    debugPrint('ApiService: Sites response - count: ${result.data.length}, total: ${result.total}');
    return result;
  }

  Future<Site> getSite(int id) async {
    final response = await _httpService.get<Site>(
      '${AppConstants.sitesEndpoint}/$id',
      fromJson: (json) => Site.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<Site> createSite(SiteCreateRequest request) async {
    final response = await _httpService.post<Site>(
      AppConstants.sitesEndpoint,
      data: request.toJson(),
      fromJson: (json) => Site.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<Site> updateSite(int id, SiteUpdateRequest request) async {
    final response = await _httpService.put<Site>(
      '${AppConstants.sitesEndpoint}/$id',
      data: request.toJson(),
      fromJson: (json) => Site.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> deleteSite(int id) async {
    final response = await _httpService.delete(
      '${AppConstants.sitesEndpoint}/$id',
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> assignSiteToUser(int userId, int siteId) async {
    final request = SiteAssignmentRequest(userId: userId, siteId: siteId);
    final response = await _httpService.post(
      '${AppConstants.sitesEndpoint}/assign',
      data: request.toJson(),
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> assignSiteToUsers(int siteId, List<int> userIds) async {
    final request = {
      'site_id': siteId,
      'user_ids': userIds,
    };

    debugPrint('ApiService: Assigning site $siteId to users: $userIds');

    final response = await _httpService.post(
      '${AppConstants.sitesEndpoint}/assign',
      data: request,
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }

    debugPrint('ApiService: Site assignment successful');
  }

  // Employees endpoints
  Future<PaginatedResponse<User>> getEmployees({
    int page = 1,
    int perPage = 20,
    String? search,
    String? role,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'per_page': perPage,
      if (search != null && search.isNotEmpty) 'search': search,
      if (role != null && role.isNotEmpty) 'role': role,
    };

    return await _httpService.getPaginated<User>(
      AppConstants.employeesEndpoint,
      queryParameters: queryParams,
      fromJson: (json) => User.fromJson(json),
    );
  }

  Future<User> getEmployee(int id) async {
    final response = await _httpService.get<User>(
      '${AppConstants.employeesEndpoint}/$id',
      fromJson: (json) => User.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<User> createEmployee(UserCreateRequest request) async {
    final response = await _httpService.post<User>(
      AppConstants.employeesEndpoint,
      data: request.toJson(),
      fromJson: (json) => User.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<User> updateEmployee(int id, UserUpdateRequest request) async {
    final response = await _httpService.put<User>(
      '${AppConstants.employeesEndpoint}/$id',
      data: request.toJson(),
      fromJson: (json) => User.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> deleteEmployee(int id) async {
    final response = await _httpService.delete(
      '${AppConstants.employeesEndpoint}/$id',
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  // Pointage endpoints
  Future<LocationCheckResponse> checkLocation(LocationCheckRequest request) async {
    final response = await _httpService.post<LocationCheckResponse>(
      AppConstants.checkLocationEndpoint,
      data: request.toJson(),
      fromJson: (json) => LocationCheckResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<PointageResponse> createPointage(PointageCreateRequest request) async {
    final response = await _httpService.post<PointageResponse>(
      AppConstants.pointageEndpoint,
      data: request.toJson(),
      fromJson: (json) => PointageResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<PaginatedResponse<Pointage>> getPointages({
    int page = 1,
    int perPage = 20,
    int? userId,
    int? siteId,
    String? dateFrom,
    String? dateTo,
    String? status,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'per_page': perPage,
      if (userId != null) 'user_id': userId,
      if (siteId != null) 'site_id': siteId,
      if (dateFrom != null) 'date_from': dateFrom,
      if (dateTo != null) 'date_to': dateTo,
      if (status != null) 'status': status,
    };

    return await _httpService.getPaginated<Pointage>(
      AppConstants.pointageEndpoint,
      queryParameters: queryParams,
      fromJson: (json) => Pointage.fromJson(json),
    );
  }

  Future<Pointage?> getActivePointage() async {
    try {
      final response = await _httpService.get<Pointage>(
        '${AppConstants.pointageEndpoint}/active',
        fromJson: (json) => Pointage.fromJson(json),
      );

      if (response.success) {
        return response.data;
      }
      return null;
    } catch (e) {
      debugPrint('Error getting active pointage: $e');
      return null;
    }
  }

  Future<PointageStats> getPointageStats({
    int? userId,
    String? dateFrom,
    String? dateTo,
  }) async {
    final queryParams = <String, dynamic>{
      if (userId != null) 'user_id': userId,
      if (dateFrom != null) 'date_from': dateFrom,
      if (dateTo != null) 'date_to': dateTo,
    };

    final response = await _httpService.get<PointageStats>(
      '${AppConstants.pointageEndpoint}/stats',
      queryParameters: queryParams,
      fromJson: (json) => PointageStats.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  // Check in
  Future<PointageResponse> checkIn(CheckInRequest request) async {
    final response = await _httpService.post<PointageResponse>(
      '${AppConstants.pointageEndpoint}/check-in',
      data: request.toJson(),
      fromJson: (json) => PointageResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  // Check out
  Future<PointageResponse> checkOut(CheckOutRequest request) async {
    final response = await _httpService.post<PointageResponse>(
      '${AppConstants.pointageEndpoint}/check-out',
      data: request.toJson(),
      fromJson: (json) => PointageResponse.fromJson(json),
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw ApiException(message: response.displayMessage);
    }
  }

  // Export pointages
  Future<void> exportPointages({
    String? dateFrom,
    String? dateTo,
    int? userId,
    int? siteId,
  }) async {
    final queryParams = <String, dynamic>{
      if (dateFrom != null) 'date_from': dateFrom,
      if (dateTo != null) 'date_to': dateTo,
      if (userId != null) 'user_id': userId,
      if (siteId != null) 'site_id': siteId,
    };

    final response = await _httpService.get(
      '${AppConstants.pointageEndpoint}/export',
      queryParameters: queryParams,
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  // Monitoring endpoints
  Future<void> checkEmployeeOnSite(int userId, double latitude, double longitude) async {
    final data = {
      'user_id': userId,
      'latitude': latitude,
      'longitude': longitude,
    };

    final response = await _httpService.post(
      '${AppConstants.monitoringEndpoint}/check-employee-on-site',
      data: data,
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> checkAllActiveEmployees() async {
    final response = await _httpService.post(
      '${AppConstants.monitoringEndpoint}/check-all-active',
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> startMonitoring(int userId, {int intervalMinutes = 15}) async {
    final data = {
      'user_id': userId,
      'interval_minutes': intervalMinutes,
    };

    final response = await _httpService.post(
      '${AppConstants.monitoringEndpoint}/start-monitoring',
      data: data,
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> stopMonitoring(int userId) async {
    final data = {'user_id': userId};

    final response = await _httpService.post(
      '${AppConstants.monitoringEndpoint}/stop-monitoring',
      data: data,
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  // Reports endpoints
  Future<void> generateEmployeeReport({
    required String startDate,
    required String endDate,
    bool includeStats = true,
  }) async {
    final data = {
      'start_date': startDate,
      'end_date': endDate,
      'include_stats': includeStats,
    };

    final response = await _httpService.post(
      '${AppConstants.reportsEndpoint}/employees',
      data: data,
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> generateEmployeeReportById(int userId, {
    required String startDate,
    required String endDate,
  }) async {
    final data = {
      'start_date': startDate,
      'end_date': endDate,
    };

    final response = await _httpService.post(
      '${AppConstants.reportsEndpoint}/employees/$userId',
      data: data,
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> generateSiteReport(int siteId, {
    required String startDate,
    required String endDate,
  }) async {
    final data = {
      'start_date': startDate,
      'end_date': endDate,
    };

    final response = await _httpService.post(
      '${AppConstants.reportsEndpoint}/sites/$siteId',
      data: data,
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  // Verification endpoints
  Future<void> requestVerification(int userId) async {
    final data = {'user_id': userId};

    final response = await _httpService.post(
      '${AppConstants.verificationEndpoint}/request',
      data: data,
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }

  Future<void> submitVerification(double latitude, double longitude) async {
    final data = {
      'latitude': latitude,
      'longitude': longitude,
    };

    final response = await _httpService.post(
      AppConstants.verificationEndpoint,
      data: data,
    );

    if (!response.success) {
      throw ApiException(message: response.displayMessage);
    }
  }
}
